import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Plus,
  MapPin,
  Clock,
  Users as UsersIcon,
  ChevronDown,
  Search
} from 'lucide-react';
import Layout from '../../components/common/Layout';
import { Event, EventFilter, EventSortOption, convertApiEventToUIEvent } from '../../types/events';
import { AppDispatch } from '../../store/store';
import { selectAuth } from '../../store/slices/authSlice';
import JoinEventCodeModal from '../../components/modals/JoinEventCodeModal';
import JoinEventModal from '../../components/modals/JoinEventModal';
import {
  selectFilteredEvents,
  selectEventsLoading,
  selectEventsError,
  selectActiveFilter,
  selectSearchTerm,
  selectSortBy,
  selectSelectedEvent,
  setActiveFilter,
  setSearchTerm,
  setSortBy,
  setSelectedEvent,
} from '../../store/slices/eventsSlice';
import {
  fetchAllEvents,
  fetchHomeEvents,
  fetchAttendedEvents,
  fetchCreatedEvents,
  joinEvent,
  leaveEvent,
  searchEvents,
} from '../../store/thunks/eventsThunks';

const Events: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [showJoinEventModal, setShowJoinEventModal] = useState(false);
  const [selectedEventForJoin, setSelectedEventForJoin] = useState<Event | null>(null);

  // Redux state
  const { tokens } = useSelector(selectAuth);
  const filteredEvents = useSelector(selectFilteredEvents) || [];
  const loading = useSelector(selectEventsLoading) || {};
  const error = useSelector(selectEventsError) || {};
  const activeTab = useSelector(selectActiveFilter) || 'all';
  const searchTerm = useSelector(selectSearchTerm) || '';
  const sortBy = useSelector(selectSortBy) || 'newest';
  const selectedEvent = useSelector(selectSelectedEvent);

  // Debug logging (can be removed in production)
  console.log('Events Debug:', {
    filteredEvents: filteredEvents.map((e: Event) => ({
      id: e.id,
      name: e.name,
      event_status: e.event_status,
      status: e.status,
      navigation: e.navigation,
      attendance_type: e.attendance_type
    })),
    loading,
    error,
    activeTab
  });

  // Fetch events data on component mount
  useEffect(() => {
    if (tokens?.access) {
      // Fetch different types of events based on the active tab
      switch (activeTab) {
        case 'all':
          dispatch(fetchAllEvents(tokens.access));
          break;
        case 'available':
          dispatch(fetchAllEvents(tokens.access));
          break;
        case 'joined':
          dispatch(fetchAttendedEvents(tokens.access));
          break;
        case 'upcoming':
          dispatch(fetchHomeEvents(tokens.access));
          break;
        default:
          dispatch(fetchAllEvents(tokens.access));
      }
    }
  }, [dispatch, tokens?.access, activeTab]);

  // Search functionality with debouncing
  useEffect(() => {
    if (!tokens?.access || !searchTerm.trim()) return;

    const searchTimeout = setTimeout(() => {
      dispatch(searchEvents({
        token: tokens.access!,
        searchData: {
          query: searchTerm.trim()
        }
      }));
    }, 500); // 500ms debounce

    return () => clearTimeout(searchTimeout);
  }, [dispatch, searchTerm, tokens?.access]);

  // Apply search filter to the Redux-filtered events
  const searchFilteredEvents = Array.isArray(filteredEvents) ? filteredEvents.filter((event: Event) => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return event.name.toLowerCase().includes(searchLower) ||
           (event.description && event.description.toLowerCase().includes(searchLower)) ||
           (event.location && event.location.toLowerCase().includes(searchLower));
  }) : [];

  // Sort events
  const sortedEvents = Array.isArray(searchFilteredEvents) ? [...searchFilteredEvents].sort((a: Event, b: Event) => {
    if (sortBy === 'newest') {
      return new Date(b.start_date || b.date || '').getTime() - new Date(a.start_date || a.date || '').getTime();
    } else if (sortBy === 'oldest') {
      return new Date(a.start_date || a.date || '').getTime() - new Date(b.start_date || b.date || '').getTime();
    } else if (sortBy === 'name') {
      return a.name.localeCompare(b.name);
    }
    return 0;
  }) : [];



  const handleEventClick = (event: Event) => {
    dispatch(setSelectedEvent(event));
  };

  const handleJoinEventClick = (event: Event) => {
    console.log('🎯 Join event button clicked for event:', event.name, event.id);
    setSelectedEventForJoin(event);
    setShowJoinEventModal(true);
  };

  const handleJoinEventModalClose = () => {
    console.log('🔒 Closing join event modal');
    setShowJoinEventModal(false);
    setSelectedEventForJoin(null);
    // Refresh events after modal closes (in case event was joined)
    if (tokens?.access) {
      dispatch(fetchAllEvents(tokens.access));
    }
  };

  const handleLeaveEvent = async (eventId: string) => {
    if (!tokens?.access) return;

    try {
      await dispatch(leaveEvent({
        eventId,
        token: tokens.access
      })).unwrap();

      // Refresh events after leaving
      dispatch(fetchAllEvents(tokens.access));
    } catch (error) {
      console.error('Failed to leave event:', error);
    }
  };

  return (
    <Layout
      searchPlaceholder="Search events..."
      showRightSidebar={true}
      selectedEvent={selectedEvent}
      onEventClose={() => setSelectedEvent(null)}
      sidebarType="event"
    >
      {/* Events Content */}
      <div className="flex-1 p-6 overflow-y-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 text-left mb-6">Events</h1>
          
          {/* Tabs and Controls */}
          <div className="flex justify-between items-center border-b border-gray-200">
            <div className="flex">
              <button
                onClick={() => dispatch(setActiveFilter('all'))}
                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                  activeTab === 'all'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                All Events
              </button>
              <button
                onClick={() => dispatch(setActiveFilter('available'))}
                className={`px-4 py-2 text-sm font-medium border-b-2 ml-8 ${
                  activeTab === 'available'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Available Events
              </button>
              <button
                onClick={() => dispatch(setActiveFilter('joined'))}
                className={`px-4 py-2 text-sm font-medium border-b-2 ml-8 ${
                  activeTab === 'joined'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Joined Events
              </button>
              <button
                onClick={() => dispatch(setActiveFilter('upcoming'))}
                className={`px-4 py-2 text-sm font-medium border-b-2 ml-8 ${
                  activeTab === 'upcoming'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Upcoming Events
              </button>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="relative">
                <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search events"
                  value={searchTerm}
                  onChange={(e) => dispatch(setSearchTerm(e.target.value))}
                  className="pl-9 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                />
              </div>
              <div className="relative">
                <select
                  value={sortBy}
                  onChange={(e) => dispatch(setSortBy(e.target.value as EventSortOption))}
                  className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="newest">Newest</option>
                  <option value="oldest">Oldest</option>
                  <option value="name">Name</option>
                  <option value="date">Date</option>
                </select>
                <ChevronDown size={16} className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
              </div>

              {/* Join Event Button */}
              <button
                onClick={() => setShowJoinModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center gap-2"
              >
                <Plus size={16} />
                Join Event
              </button>
            </div>
          </div>
        </div>

        {/* Loading State */}
        {loading?.allEvents && (
          <div className="flex justify-center items-center py-12">
            <div className="text-gray-500">Loading events...</div>
          </div>
        )}

        {/* Error State */}
        {error?.allEvents && (
          <div className="flex justify-center items-center py-12">
            <div className="text-red-500">Error loading events: {error.allEvents}</div>
          </div>
        )}

        {/* Events Grid */}
        {!loading?.allEvents && !error?.allEvents && (
          <div className="grid grid-cols-4 gap-6">
            {sortedEvents.length === 0 ? (
              <div className="col-span-4 text-center py-12 text-gray-500">
                No events found matching your criteria.
              </div>
            ) : (
              sortedEvents.map((event) => {
                // Determine event status based on new logic with fallback
                const getEventStatus = () => {
                  // Debug: Log event data to see what we're working with
                  console.log('🔍 Event data for status determination:', {
                    eventName: event.name,
                    navigation: event.navigation,
                    upload_permissions: event.upload_permissions,
                    attendance_type: event.attendance_type,
                    event_status: event.event_status,
                    status: event.status
                  });

                  // New API-based logic
                  if (event.navigation?.user_role === 'NON_ATTENDEE' && event.upload_permissions?.reason === 'Event is active') {
                    return 'Available'
                  } else if (event.navigation?.user_role === 'PHOTOGRAPHER' && event.upload_permissions?.reason === 'Event is active') {
                    return 'Ongoing'
                  } else if (event.navigation?.user_role === 'PHOTOGRAPHER' && event.upload_permissions?.reason === 'Upload deadline has passed') {
                    return 'Completed'
                  } else if (event.navigation?.user_role === 'PHOTOGRAPHER' && event.upload_permissions?.reason?.includes('Event starts in')) {
                    return 'Scheduled'
                  } else if (event.navigation?.user_role === 'NON_ATTENDEE' && event.upload_permissions?.reason?.includes('Event starts in')) {
                    return 'Available'
                  } else {
                    // Fallback to old logic if new fields are not available
                    console.log('⚠️ Using fallback status logic for event:', event.name);

                    if (event.navigation?.action === 'SHOW_JOIN_PROMPT') {
                      return 'Available'
                    } else if (event.attendance_type === 'Attended') {
                      return 'Ongoing'
                    } else if (event.attendance_type === 'Completed' || event.status === 'completed') {
                      return 'Completed'
                    } else if (event.status === 'upcoming' || event.event_status === 'scheduled') {
                      return 'Scheduled'
                    } else {
                      return 'Available' // Default to Available for unknown events
                    }
                  }
                }

                const eventStatus = getEventStatus()
                const shouldShowJoinButton = event.navigation?.user_role === 'NON_ATTENDEE' && event.upload_permissions?.reason === 'Event is active' ||
                  event.navigation?.action === 'SHOW_JOIN_PROMPT' || eventStatus === 'Available'

                return (
            <div
              key={event.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleEventClick(event)}
            >
              {/* Banner Image */}
              <div className="w-full h-32 bg-gray-300 relative overflow-hidden">
                {event.banner_image || event.bannerImage ? (
                  <img
                    src={event.banner_image || event.bannerImage}
                    alt={event.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // Fallback to placeholder if image fails to load
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-blue-200">
                    <span className="text-blue-600 font-bold text-2xl">
                      {event.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
              
              {/* Event Content */}
              <div className="p-4">
                <div className="mb-2">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 text-left">{event.name}</h3>
                    {/* Event Status Indicator */}
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      eventStatus === 'Available'
                        ? 'text-blue-600 bg-blue-100'
                        : eventStatus === 'Ongoing'
                        ? 'text-green-600 bg-green-100'
                        : eventStatus === 'Completed'
                        ? 'text-red-600 bg-red-100'
                        : eventStatus === 'Scheduled'
                        ? 'text-yellow-600 bg-yellow-100'
                        : 'text-gray-600 bg-gray-100'
                    }`}>
                      {eventStatus}
                    </span>
                  </div>
                  <div className="w-full h-px bg-gray-200"></div>
                </div>

                <p className="text-sm text-gray-600 text-left mb-3 line-clamp-2">{event.description || 'No description available'}</p>

                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock size={14} className="mr-2" />
                    {event.date || new Date(event.start_date).toLocaleDateString()} at {event.time || new Date(event.start_date).toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true })}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <MapPin size={14} className="mr-2" />
                    {event.location || 'Location TBD'}
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <UsersIcon size={14} className="mr-2" />
                    {event.attendees || 0} attendees
                  </div>
                </div>

                {/* Event Actions */}
                {shouldShowJoinButton && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleJoinEventClick(event);
                      }}
                      className="w-full bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-700 transition-colors"
                    >
                      Join Event
                    </button>
                  </div>
                )}

                {event.navigation?.user_role === 'PHOTOGRAPHER' && (
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleLeaveEvent(event.id);
                      }}
                      className="w-full bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                    >
                      Leave Event
                    </button>
                  </div>
                )}

                {/* Revenue info for all events */}
                <div className="mt-3 pt-3 border-t border-gray-200 mb-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Revenue Generated</span>
                    <span className="text-sm font-medium text-green-600">
                      {(eventStatus === 'Completed' || eventStatus === 'Ongoing')
                        ? (event.revenue ||
                           (event.event_type === 'WEDDING' ? '$1,250' :
                            event.event_type === 'CORPORATE' ? '$850' :
                            event.event_type === 'SPORTS' ? '$425' :
                            '$250'))
                        : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-xs text-gray-400">Revenue Type</span>
                    <span className="text-xs text-gray-600">
                      {event.revenueType ||
                        (event.event_type === 'WEDDING' ? 'Commission Based' :
                         event.event_type === 'CORPORATE' ? 'Fixed Rate' :
                         event.event_type === 'SPORTS' ? 'Pay Per Photo' :
                         'Pay Per Photo')}
                    </span>
                  </div>
                </div>
              </div>
            </div>
                )
              })
            )}
          </div>
        )}
      </div>

      {/* Join Event Code Modal */}
      <JoinEventCodeModal
        isOpen={showJoinModal}
        onClose={() => setShowJoinModal(false)}
      />

      {/* Join Event Modal */}
      <JoinEventModal
        isOpen={showJoinEventModal}
        onClose={handleJoinEventModalClose}
        event={selectedEventForJoin}
      />
    </Layout>
  );
};

export default Events;
