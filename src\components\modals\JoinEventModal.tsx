import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { X, UserPlus, Hash } from 'lucide-react';
import { Event } from '../../types/events';
import { AppDispatch } from '../../store/store';
import { selectAuth } from '../../store/slices/authSlice';
import { joinEvent } from '../../store/thunks/eventsThunks';

interface JoinEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  event: Event | null;
}

const JoinEventModal: React.FC<JoinEventModalProps> = ({ isOpen, onClose, event }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { tokens } = useSelector(selectAuth);
  const [portfolioLink, setPortfolioLink] = useState('');
  const [specialization, setSpecialization] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  if (!isOpen || !event) {
    console.log('🚫 JoinEventModal not rendering:', { isOpen, hasEvent: !!event });
    return null;
  }

  console.log('✅ JoinEventModal rendering for event:', event.name, event.id);

  const handleJoinEvent = async () => {
    if (!tokens?.access || !event) {
      console.error('Missing tokens or event:', { tokens: !!tokens?.access, event: !!event });
      return;
    }

    setIsLoading(true);
    setMessage('');

    try {
      const joinData: any = {};
      if (portfolioLink.trim()) {
        joinData.portfolio_link = portfolioLink.trim();
      }
      if (specialization.trim()) {
        joinData.specialization = specialization.trim();
      }

      console.log('🚀 Starting join event process:', {
        eventId: event.id,
        eventName: event.name,
        joinData,
        hasToken: !!tokens.access
      });

      const response = await dispatch(joinEvent({
        eventId: event.id,
        token: tokens.access,
        data: joinData
      })).unwrap();

      console.log('✅ Join event successful:', response);

      setIsSuccess(true);
      setMessage(response.response.message || 'Successfully joined event as photographer.');

      // Close modal after 3 seconds
      setTimeout(() => {
        onClose();
        setMessage('');
        setPortfolioLink('');
        setSpecialization('');
        setIsSuccess(false);
      }, 3000);

    } catch (error: any) {
      console.error('❌ Join event failed:', error);
      setIsSuccess(false);
      setMessage(error.message || 'Failed to join event. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-96 max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <UserPlus size={16} className="text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Join as Photographer</h3>
              <p className="text-xs text-gray-500">{event?.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-lg"
          >
            <X size={16} className="text-gray-400" />
          </button>
        </div>

        {/* Content */}
        <div className="p-4">
          {/* Event Information */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-6 h-6 bg-blue-100 rounded flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-xs">
                  {event?.name?.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">{event?.name}</p>
                <p className="text-xs text-gray-500">
                  {event?.start_date && new Date(event.start_date).toLocaleDateString()} • {event?.location}
                </p>
              </div>
            </div>
            <p className="text-sm text-gray-700">
              Join this event as a photographer to upload photos and manage your content. You'll get full photographer permissions.
            </p>
          </div>

          {/* Portfolio Link Input (Optional) */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Portfolio Link (Optional)
            </label>
            <input
              type="url"
              value={portfolioLink}
              onChange={(e) => setPortfolioLink(e.target.value)}
              placeholder="https://your-portfolio.com"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-500 mt-1">
              Share your portfolio to showcase your work
            </p>
          </div>

          {/* Specialization Input (Optional) */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Specialization (Optional)
            </label>
            <select
              value={specialization}
              onChange={(e) => setSpecialization(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select your specialization</option>
              <option value="wedding_photography">Wedding Photography</option>
              <option value="corporate_photography">Corporate Photography</option>
              <option value="sports_photography">Sports Photography</option>
              <option value="party_photography">Party Photography</option>
              <option value="conference_photography">Conference Photography</option>
              <option value="other">Other</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Help event organizers understand your expertise
            </p>
          </div>

          {/* Success/Error Message */}
          {message && (
            <div className={`p-3 rounded-lg mb-4 text-sm ${
              isSuccess
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {message}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              onClick={handleJoinEvent}
              disabled={isLoading || isSuccess}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Joining...
                </>
              ) : isSuccess ? (
                'Joined Successfully!'
              ) : (
                <>
                  <UserPlus size={16} />
                  Join as Photographer
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JoinEventModal;
